# GameFlex Auth Users Initialization Script (PowerShell)
# This script creates development users using Supabase Auth API
# It runs after all services are up and the auth service is ready

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Auth Users Initialization Script" -ForegroundColor Green
    Write-Host "Usage: .\init-auth-users.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script creates development users in Supabase Auth for testing."
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🔐 Initializing GameFlex development users in Supabase Auth..." -ForegroundColor Green

# Load environment variables from .env file
$envPath = if (Test-Path ".env") { ".env" } elseif (Test-Path "..\\.env") { "..\\.env" } else { $null }

if (-not $envPath) {
    Write-Host "❌ .env file not found" -ForegroundColor Red
    exit 1
}

$envVars = @{}
Get-Content $envPath | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $envVars[$matches[1]] = $matches[2]
    }
}

# Set default ports if not specified
$KONG_HTTP_PORT = if ($envVars["KONG_HTTP_PORT"]) { $envVars["KONG_HTTP_PORT"] } else { "8000" }
$SERVICE_ROLE_KEY = $envVars["SERVICE_ROLE_KEY"]
$ANON_KEY = $envVars["ANON_KEY"]

# Wait for database to be ready
Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
$dbReady = $false
$attempts = 0
$maxAttempts = 30

while (-not $dbReady -and $attempts -lt $maxAttempts) {
    try {
        docker-compose exec -T db pg_isready -U postgres -h localhost 2>$null | Out-Null
        $dbReady = $LASTEXITCODE -eq 0
    } catch {
        $dbReady = $false
    }
    
    if (-not $dbReady) {
        Write-Host "Database is not ready yet, waiting..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($dbReady) {
    Write-Host "✅ Database is ready!" -ForegroundColor Green
} else {
    Write-Host "❌ Database failed to start within expected time" -ForegroundColor Red
    exit 1
}

# Wait for auth service to be ready
Write-Host "⏳ Waiting for auth service to be ready..." -ForegroundColor Yellow
$authReady = $false
$attempts = 0

while (-not $authReady -and $attempts -lt $maxAttempts) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/settings" -Headers @{"apikey" = $ANON_KEY} -TimeoutSec 5 -UseBasicParsing
        $authReady = $response.StatusCode -eq 200
    } catch {
        $authReady = $false
    }
    
    if (-not $authReady) {
        Write-Host "Auth service is not ready yet, waiting..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($authReady) {
    Write-Host "✅ Auth service is ready!" -ForegroundColor Green
} else {
    Write-Host "❌ Auth service failed to start within expected time" -ForegroundColor Red
    exit 1
}

# Function to create a user using direct database insertion with proper GoTrue-compatible hashing
function Create-AuthUser {
    param(
        [string]$UserId,
        [string]$Email,
        [string]$Password,
        [string]$DisplayName
    )

    Write-Host "👤 Creating auth user: $Email" -ForegroundColor Yellow

    # Check if user already exists
    try {
        $existingCountResult = docker-compose exec -T db psql -U postgres -d postgres -c "SELECT COUNT(*) FROM auth.users WHERE email = '$Email';" -t 2>$null
        $existingCount = [int]($existingCountResult.Trim())

        if ($existingCount -gt 0) {
            Write-Host "   ✅ User $Email already exists" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "   ⚠️  Could not check if user exists" -ForegroundColor Yellow
    }

    # Create the user using direct database insertion with proper bcrypt hashing and fixed UUID
    $sqlCommand = @"
    INSERT INTO auth.users (
        id,
        instance_id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        role,
        aud,
        confirmation_token,
        recovery_token,
        email_change,
        email_change_token_new,
        email_change_token_current,
        phone_change_token,
        reauthentication_token
    ) VALUES (
        '$UserId',
        '00000000-0000-0000-0000-000000000000',
        '$Email',
        crypt('$Password', gen_salt('bf', 10)),
        NOW(),
        NOW(),
        NOW(),
        '{\"provider\": \"email\", \"providers\": [\"email\"]}',
        '{\"display_name\": \"$DisplayName\"}',
        false,
        'authenticated',
        'authenticated',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
    );
"@

    try {
        docker-compose exec -T db psql -U postgres -d postgres -c $sqlCommand 2>$null | Out-Null
    } catch {
        Write-Host "   ❌ Failed to create user: $Email" -ForegroundColor Red
        return $false
    }

    # Verify user was created
    try {
        $newCountResult = docker-compose exec -T db psql -U postgres -d postgres -c "SELECT COUNT(*) FROM auth.users WHERE email = '$Email';" -t 2>$null
        $newCount = [int]($newCountResult.Trim())

        if ($newCount -gt 0) {
            Write-Host "   ✅ Successfully created user: $Email" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ❌ Failed to create user: $Email" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ❌ Failed to verify user creation: $Email" -ForegroundColor Red
        return $false
    }
}

# Create development users with matching UUIDs from public.users table
Write-Host "🔄 Creating development users..." -ForegroundColor Yellow
$users = @(
    @{ Id = "00000000-0000-0000-0000-000000000001"; Email = "<EMAIL>"; Password = "devpassword123"; DisplayName = "Development User" },
    @{ Id = "00000000-0000-0000-0000-000000000002"; Email = "<EMAIL>"; Password = "adminpassword123"; DisplayName = "Admin User" },
    @{ Id = "00000000-0000-0000-0000-000000000003"; Email = "<EMAIL>"; Password = "johnpassword123"; DisplayName = "John Doe" },
    @{ Id = "00000000-0000-0000-0000-000000000004"; Email = "<EMAIL>"; Password = "janepassword123"; DisplayName = "Jane Smith" },
    @{ Id = "00000000-0000-0000-0000-000000000005"; Email = "<EMAIL>"; Password = "mikepassword123"; DisplayName = "Mike Wilson" }
)

$successCount = 0
foreach ($user in $users) {
    if (Create-AuthUser -UserId $user.Id -Email $user.Email -Password $user.Password -DisplayName $user.DisplayName) {
        $successCount++
    }
}

Write-Host ""
Write-Host "🔧 Fixing NULL values in auth.users table for GoTrue compatibility..." -ForegroundColor Yellow
# Fix all nullable string columns that GoTrue v2.174.0+ requires to be empty strings instead of NULL
$fixNullSql = @"
UPDATE auth.users SET
  aud = COALESCE(aud, ''),
  role = COALESCE(role, ''),
  confirmation_token = COALESCE(confirmation_token, ''),
  recovery_token = COALESCE(recovery_token, ''),
  email_change_token_new = COALESCE(email_change_token_new, ''),
  email_change = COALESCE(email_change, ''),
  phone_change_token = COALESCE(phone_change_token, ''),
  email_change_token_current = COALESCE(email_change_token_current, ''),
  reauthentication_token = COALESCE(reauthentication_token, '')
WHERE id IS NOT NULL;
"@

try {
    docker-compose exec -T db psql -U postgres -d postgres -c $fixNullSql 2>$null | Out-Null
    Write-Host "   ✅ Successfully fixed NULL values in auth.users table" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Warning: Could not fix NULL values in auth.users table" -ForegroundColor Yellow
}

# Verify users were created
Write-Host ""
Write-Host "🔍 Verifying auth users were created..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/admin/users" -Headers @{
        "Authorization" = "Bearer $SERVICE_ROLE_KEY"
        "apikey" = $SERVICE_ROLE_KEY
    } -UseBasicParsing

    $userCount = ($response.Content | Select-String '"email"' -AllMatches).Matches.Count

    if ($userCount -ge 5) {
        Write-Host "✅ Successfully created/verified $userCount auth users!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎮 GameFlex Development Users:" -ForegroundColor Cyan
        Write-Host "   📧 <EMAIL> (password: devpassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: adminpassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: johnpassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: janepassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: mikepassword123)" -ForegroundColor White
        Write-Host ""
        Write-Host "🚀 You can now log in to GameFlex with any of these accounts!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Warning: Expected at least 5 users but found $userCount" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Warning: Could not verify user count via API" -ForegroundColor Yellow
}

Write-Host "✅ Auth users initialization complete!" -ForegroundColor Green
